"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import Sidebar from "../../components/sidebar/sidebar";
import AIPanel from "../../components/ai-panel/AI-panel";
// 删除 PanelItem 导入
// 导入面板组件
import Chat from "../../components/ai-panel/panels/ChatPanel";
import Docs from "../../components/ai-panel/panels/DocsPanel";
import Settings from "../../components/ai-panel/panels/SettingsPanel";
import Tools from "../../components/ai-panel/panels/TodoPanel";

import MobilePanel from "../../components/ui/MobilePanel";
import Divider from "../../components/ui/Divider";
import Editor from "../../components/editor/editor";
import ContextMenu from "../../components/ui/DropdownMenu/ContextMenu";
import MenuItem from "../../components/ui/DropdownMenu/MenuItem";
import TopNav from "../../components/top-nav/TopNav";
import useResizablePanel from "../../hooks/panels/useResizablePanel";
import useCollapsiblePanel from "../../hooks/panels/useCollapsiblePanel";
import MobileToolbar from "../../components/editor/components/mobile-toolbar/MobileToolbar";
import { useEditorFocus } from "../../hooks/editor/use-editor-focus";
import { Editor as TiptapEditor } from "@tiptap/react";
import { tiptapToMarkdown } from "../../utils/document-converter";

// 本地存储键名
const STORAGE_KEY_AI_PANEL = "framesound-ai-panel-width";
const STORAGE_KEY_SIDEBAR = "framesound-sidebar-width";
const STORAGE_KEY_SIDEBAR_COLLAPSED = "framesound-sidebar-collapsed";
const STORAGE_KEY_AI_PANEL_COLLAPSED = "framesound-ai-panel-collapsed";

export default function EditorPage() {
  // 客户端渲染状态
  const [mounted, setMounted] = useState(false);

  // 当前打开的文件路径 - 默认空，从Sidebar获取
  const [currentFilePath, setCurrentFilePath] = useState<string[]>([]);

  // 移动端面板状态
  const [isMobileView, setIsMobileView] = useState(false);
  const [mobilePanel, setMobilePanel] = useState<{
    isOpen: boolean;
    activePanel: "sidebar" | "aiPanel" | null;
  }>({
    isOpen: false,
    activePanel: null,
  });

  // 输入法状态
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [viewportHeight, setViewportHeight] = useState(0);

  // 编辑器实例
  const [editorInstance, setEditorInstance] = useState<TiptapEditor | null>(
    null
  );

  // 编辑器焦点状态
  const editorFocus = useEditorFocus(editorInstance);

  const DEFAULT_PANEL_WIDTH = 440;
  const DEFAULT_SIDEBAR_WIDTH = 250;
  const ANIMATION_DURATION = 300; // 动画持续时间(ms)
  const containerRef = useRef<HTMLDivElement>(null);

  // 检测输入法高度和视口变化
  const detectKeyboardAndViewport = useCallback(() => {
    if (typeof window !== "undefined" && window.visualViewport) {
      const viewportHeight = window.visualViewport.height;
      const windowHeight = window.innerHeight;

      // 当视口高度小于窗口高度时，说明输入法可能已弹出
      if (viewportHeight < windowHeight) {
        const estimatedKeyboardHeight = windowHeight - viewportHeight;
        setKeyboardHeight(estimatedKeyboardHeight);
      } else {
        setKeyboardHeight(0);
      }

      // 更新当前视口高度
      setViewportHeight(viewportHeight);
    }
  }, []);

  // 使用自定义Hook处理AI Panel的拖拽和尺寸调整
  const aiPanel = useResizablePanel({
    defaultWidth: DEFAULT_PANEL_WIDTH,
    minWidth: 380,
    maxWidthRatio: 0.3,
    storageKey: STORAGE_KEY_AI_PANEL,
    containerRef,
    direction: "right", // 从右向左拖拽
  });

  // 使用自定义Hook处理Sidebar的拖拽和尺寸调整
  const sidebar = useResizablePanel({
    defaultWidth: DEFAULT_SIDEBAR_WIDTH,
    minWidth: 250,
    maxWidthRatio: 0.2,
    storageKey: STORAGE_KEY_SIDEBAR,
    containerRef,
    direction: "left", // 从左向右拖拽
  });

  // 使用自定义Hook处理Sidebar的折叠/展开
  const sidebarCollapsible = useCollapsiblePanel({
    resizablePanel: sidebar,
    defaultWidth: DEFAULT_SIDEBAR_WIDTH,
    storageKeyCollapsed: STORAGE_KEY_SIDEBAR_COLLAPSED,
    storageKeyWidthBeforeCollapse: `${STORAGE_KEY_SIDEBAR}-before-collapse`,
    animationDuration: ANIMATION_DURATION,
  });

  // 使用自定义Hook处理AI Panel的折叠/展开
  const aiPanelCollapsible = useCollapsiblePanel({
    resizablePanel: aiPanel,
    defaultWidth: DEFAULT_PANEL_WIDTH,
    storageKeyCollapsed: STORAGE_KEY_AI_PANEL_COLLAPSED,
    storageKeyWidthBeforeCollapse: `${STORAGE_KEY_AI_PANEL}-before-collapse`,
    animationDuration: ANIMATION_DURATION,
    minCollapsedWidth: 32,
  });

  // 点击其他区域关闭上下文菜单
  const handleClickOutside = () => {
    if (aiPanel.showContextMenu) {
      aiPanel.setShowContextMenu(false);
    }
    if (sidebar.showContextMenu) {
      sidebar.setShowContextMenu(false);
    }
  };

  // 处理从Sidebar选中文件的回调
  const handleFileSelect = (filePath: string) => {
    if (!filePath) {
      setCurrentFilePath([]);
      return;
    }

    // 将文件路径拆分为数组，例如: "/project-files/src/components/Button.tsx" => ["project-files", "src", "components", "Button.tsx"]
    const pathSegments = filePath
      .split("/")
      .filter((segment) => segment.length > 0);
    setCurrentFilePath(pathSegments);

    // 在移动端视图下选择文件后关闭面板
    if (isMobileView && mobilePanel.isOpen) {
      setMobilePanel((prev) => ({ ...prev, isOpen: false }));
    }

    // 这里可以添加其他逻辑，如加载文件内容到编辑器等
  };

  // 处理窗口尺寸变化，判断是否为移动端视图
  const handleResize = useCallback(() => {
    setIsMobileView(window.innerWidth <= 540);
    detectKeyboardAndViewport();
  }, [detectKeyboardAndViewport]);

  // 处理移动端面板切换
  const handleMobilePanelToggle = (panelType: "sidebar" | "aiPanel") => {
    setMobilePanel((prev) => {
      // 如果当前已经打开了这个面板，则关闭它
      if (prev.isOpen && prev.activePanel === panelType) {
        return { isOpen: false, activePanel: null };
      }
      // 否则，打开或切换面板
      return { isOpen: true, activePanel: panelType };
    });
  };

  // 关闭移动端面板
  const handleCloseMobilePanel = () => {
    setMobilePanel({ isOpen: false, activePanel: null });
  };

  // 响应式处理移动端/桌面端的面板切换
  const handleToggleSidebar = () => {
    if (isMobileView) {
      handleMobilePanelToggle("sidebar");
    } else {
      sidebarCollapsible.toggleCollapse();
    }
  };

  const handleToggleAiPanel = () => {
    if (isMobileView) {
      handleMobilePanelToggle("aiPanel");
    } else {
      aiPanelCollapsible.toggleCollapse();
    }
  };

  // 处理编辑器准备好的回调
  const handleEditorReady = (editor: TiptapEditor) => {
    setEditorInstance(editor);
  };

  // 复制为 Markdown 的处理函数
  const handleCopyAsMarkdown = async () => {
    if (!editorInstance) {
      alert("Editor not ready");
      return;
    }

    try {
      // 获取编辑器的 JSON 内容
      const editorJson = editorInstance.getJSON();

      // 调试：输出编辑器的 JSON 结构
      console.log("Editor JSON:", JSON.stringify(editorJson, null, 2));

      // 获取编辑器的 HTML 内容
      const editorHtml = editorInstance.getHTML();
      console.log("Editor HTML:", editorHtml);

      // 尝试转换为 Markdown
      let markdown = "";
      try {
        markdown = tiptapToMarkdown(editorJson);
        console.log("Generated Markdown:", markdown);
      } catch (conversionError) {
        console.error("Conversion error:", conversionError);
        // 如果 JSON 转换失败，尝试简单的 HTML 转换
        markdown = htmlToSimpleMarkdown(editorHtml);
        console.log("Fallback Markdown:", markdown);
      }

      if (!markdown || markdown.trim() === "") {
        alert("No content to copy or conversion failed");
        return;
      }

      // 复制到剪贴板
      await navigator.clipboard.writeText(markdown);

      // 显示成功提示
      alert("Markdown copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy markdown:", error);
      alert(
        `Failed to copy markdown: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  };

  // 简单的 HTML 到 Markdown 转换（备用方案）
  const htmlToSimpleMarkdown = (html: string): string => {
    return (
      html
        // 预处理：移除不必要的标签和属性
        .replace(/<\/?div[^>]*>/g, "")
        .replace(/<\/?span[^>]*>/g, "")
        .replace(/<br\s*\/?>/g, "\n")
        .replace(/&nbsp;/g, " ")
        .replace(/&quot;/g, '"')
        .replace(/&amp;/g, "&")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")

        // 标题转换（确保换行）
        .replace(
          /<h1[^>]*class="[^"]*title-heading[^"]*"[^>]*>(.*?)<\/h1>/g,
          "\n# $1\n\n"
        )
        .replace(/<h1[^>]*>(.*?)<\/h1>/g, "\n# $1\n\n")
        .replace(/<h2[^>]*>(.*?)<\/h2>/g, "\n## $1\n\n")
        .replace(/<h3[^>]*>(.*?)<\/h3>/g, "\n### $1\n\n")
        .replace(/<h4[^>]*>(.*?)<\/h4>/g, "\n#### $1\n\n")
        .replace(/<h5[^>]*>(.*?)<\/h5>/g, "\n##### $1\n\n")
        .replace(/<h6[^>]*>(.*?)<\/h6>/g, "\n###### $1\n\n")

        // 段落转换（处理对齐方式）
        .replace(
          /<p[^>]*style="[^"]*text-align:\s*center[^"]*"[^>]*>(.*?)<\/p>/g,
          "\n$1\n\n"
        )
        .replace(
          /<p[^>]*style="[^"]*text-align:\s*right[^"]*"[^>]*>(.*?)<\/p>/g,
          "\n$1\n\n"
        )
        .replace(
          /<p[^>]*style="[^"]*text-align:\s*justify[^"]*"[^>]*>(.*?)<\/p>/g,
          "\n$1\n\n"
        )
        .replace(/<p[^>]*>(.*?)<\/p>/g, "\n$1\n\n")

        // 格式化标记
        .replace(/<strong[^>]*>(.*?)<\/strong>/g, "**$1**")
        .replace(/<b[^>]*>(.*?)<\/b>/g, "**$1**")
        .replace(/<em[^>]*>(.*?)<\/em>/g, "*$1*")
        .replace(/<i[^>]*>(.*?)<\/i>/g, "*$1*")
        .replace(/<s[^>]*>(.*?)<\/s>/g, "~~$1~~")
        .replace(/<del[^>]*>(.*?)<\/del>/g, "~~$1~~")
        .replace(/<code[^>]*>(.*?)<\/code>/g, "`$1`")

        // 高亮转换
        .replace(/<mark[^>]*>(.*?)<\/mark>/g, "==$1==")

        // 下划线转换
        .replace(/<u[^>]*>(.*?)<\/u>/g, "<u>$1</u>")

        // 列表转换
        .replace(/<ul[^>]*>(.*?)<\/ul>/g, (_match: string, content: string) => {
          const items = content.replace(/<li[^>]*>(.*?)<\/li>/g, "- $1");
          return `\n${items}\n\n`;
        })
        .replace(/<ol[^>]*>(.*?)<\/ol>/g, (_match: string, content: string) => {
          let counter = 1;
          const items = content.replace(
            /<li[^>]*>(.*?)<\/li>/g,
            () => `${counter++}. $1`
          );
          return `\n${items}\n\n`;
        })

        // 引用块转换
        .replace(
          /<blockquote[^>]*>(.*?)<\/blockquote>/g,
          (_match: string, content: string) => {
            return (
              content
                .split("\n")
                .map((line) => (line.trim() ? `> ${line.trim()}` : ">"))
                .join("\n") + "\n\n"
            );
          }
        )

        // 链接转换
        .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/g, "[$2]($1)")

        // 清理多余的换行和空格
        .replace(/\n{4,}/g, "\n\n\n")
        .replace(/^\s+|\s+$/g, "")
        .replace(/^[\n\r]+|[\n\r]+$/g, "")
        .trim()
    );
  };

  // 客户端渲染初始化
  useEffect(() => {
    setMounted(true);

    // 初始化检查是否为移动端
    handleResize();

    // 添加窗口尺寸变化监听
    window.addEventListener("resize", handleResize);

    // 添加视口变化监听，用于检测输入法
    if (window.visualViewport) {
      window.visualViewport.addEventListener(
        "resize",
        detectKeyboardAndViewport
      );
    }

    // 添加全局点击事件监听器来关闭上下文菜单
    const handleGlobalClick = () => {
      aiPanel.setShowContextMenu(false);
      sidebar.setShowContextMenu(false);
    };

    document.addEventListener("click", handleGlobalClick);

    // 组件卸载时清理事件监听器
    return () => {
      document.removeEventListener("click", handleGlobalClick);
      window.removeEventListener("resize", handleResize);
      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          detectKeyboardAndViewport
        );
      }
      document.body.classList.remove("select-none");
    };
  }, [handleResize, detectKeyboardAndViewport]);

  // 客户端渲染检查 - 返回加载占位符
  if (!mounted) {
    return (
      <main className="fixed inset-0 flex items-center justify-center overflow-hidden"></main>
    );
  }

  // 计算顶部导航栏高度 - 通常是48px
  const TOP_NAV_HEIGHT = 48;

  // 移动端布局 - 使用flex布局而不是fixed定位
  if (isMobileView) {
    // 计算内容区域可用高度
    const contentHeight = editorFocus.shouldShowMobileToolbar
      ? viewportHeight
      : viewportHeight - TOP_NAV_HEIGHT;

    return (
      <main
        className="flex flex-col overflow-hidden bg-backgroundDeep"
        style={{ height: `${viewportHeight}px` }}
      >
        {/* TopNav 顶部导航栏 - 使用fixed定位，在编辑状态下隐藏 */}
        {!editorFocus.shouldShowMobileToolbar && (
          <div className="fixed top-0 left-0 right-0 w-full z-40 bg-backgroundDeep">
            <TopNav
              currentFilePath={currentFilePath}
              isSidebarCollapsed={sidebarCollapsible.isCollapsed}
              isAiPanelCollapsed={aiPanelCollapsible.isCollapsed}
              onToggleSidebar={handleToggleSidebar}
              onToggleAiPanel={handleToggleAiPanel}
            />
          </div>
        )}

        {/* 主内容区域 - 根据导航栏是否显示调整padding */}
        <div
          className={`flex-1 flex flex-col overflow-hidden ${
            editorFocus.shouldShowMobileToolbar ? "pt-3 pb-10" : "pt-[48px]"
          }`}
        >
          <div
            ref={containerRef}
            className="flex-1 flex overflow-hidden px-3 pb-3 gap-1"
          >
            {/* Editor */}
            <div
              className={`
                flex-1 h-full overflow-hidden bg-background p-0 rounded-xl
                ${mobilePanel.isOpen ? "opacity-50" : "opacity-100"}
                transition-all duration-300
              `}
            >
              <div className="h-full flex flex-col">
                {/* 测试按钮 */}
                <div className="p-3 border-b border-gray-200">
                  <button
                    onClick={handleCopyAsMarkdown}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                  >
                    Copy as Markdown
                  </button>
                </div>
                <div className="flex-1 overflow-hidden">
                  <Editor onEditorReady={handleEditorReady} />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 移动端浮层面板 */}
        <MobilePanel
          isOpen={mobilePanel.isOpen}
          activePanel={mobilePanel.activePanel}
          onClose={handleCloseMobilePanel}
          onFilePathChange={handleFileSelect}
        />

        {/* 移动端工具栏 */}
        <MobileToolbar
          editor={editorInstance}
          isActive={editorFocus.shouldShowMobileToolbar}
        />
      </main>
    );
  }

  // 桌面端布局 - 保持原有的fixed布局
  return (
    <main
      className="fixed inset-0 flex flex-col bg-backgroundDeep overflow-hidden"
      onClick={handleClickOutside}
    >
      {/* TopNav 顶部导航栏 */}
      <TopNav
        currentFilePath={currentFilePath}
        isSidebarCollapsed={sidebarCollapsible.isCollapsed}
        isAiPanelCollapsed={aiPanelCollapsible.isCollapsed}
        onToggleSidebar={handleToggleSidebar}
        onToggleAiPanel={handleToggleAiPanel}
      />

      <div
        ref={containerRef}
        className="flex flex-1 overflow-hidden pl-3 pr-2 pb-3 gap-1"
      >
        {/* 桌面端 Sidebar - 只在非移动端且未折叠时显示 */}
        {(!sidebarCollapsible.isCollapsed ||
          sidebarCollapsible.isAnimating) && (
          <>
            <div
              className={`h-full overflow-hidden ${
                sidebar.isDragging
                  ? "transition-none"
                  : "transition-all duration-300 ease-in-out"
              }`}
              style={{
                width: `${sidebar.width}px`,
                flexShrink: 0,
                opacity: sidebar.width === 0 ? 0 : 1,
              }}
            >
              <Sidebar onFilePathChange={handleFileSelect} />
            </div>

            {/* 拖拽调整大小的分隔条 - Sidebar */}
            <Divider
              ref={sidebar.dividerRef}
              onDragStart={sidebar.handleDragStart}
              onContextMenu={sidebar.handleContextMenu}
              isDragging={sidebar.isDragging}
              isResetting={sidebar.isResetting}
              isMenuOpen={sidebar.showContextMenu}
              className={`${
                sidebar.isDragging
                  ? "transition-none"
                  : "transition-opacity duration-300 ease-in-out"
              } ${sidebar.width === 0 ? "opacity-0" : "opacity-100"}`}
            />
          </>
        )}

        {/* Editor */}
        <div
          className={`
          flex-1 h-full overflow-hidden bg-background p-0 rounded-xl
          ${mobilePanel.isOpen ? "opacity-50" : "opacity-100"}
          transition-all duration-300
        `}
        >
          <div className="h-full flex flex-col">
            {/* 测试按钮 */}
            <div className="p-3 border-b border-gray-200">
              <button
                onClick={handleCopyAsMarkdown}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Copy as Markdown
              </button>
            </div>
            <div className="flex-1 overflow-hidden">
              <Editor onEditorReady={handleEditorReady} />
            </div>
          </div>
        </div>

        {/* 右侧分隔条区域 - 保持间距 */}
        {!aiPanelCollapsible.isCollapsed ? (
          /* 未折叠状态 - 显示分隔条 */
          <Divider
            ref={aiPanel.dividerRef}
            onDragStart={aiPanel.handleDragStart}
            onContextMenu={aiPanel.handleContextMenu}
            isDragging={aiPanel.isDragging}
            isResetting={aiPanel.isResetting}
            isMenuOpen={aiPanel.showContextMenu}
            className={`${
              aiPanel.isDragging
                ? "transition-none"
                : "transition-opacity duration-300 ease-in-out"
            }`}
          />
        ) : (
          /* 折叠状态 - 仅提供间距 */
          <div className="w-[0.5px] flex-shrink-0"></div>
        )}

        {/* AI Panel - 始终显示，但根据折叠状态调整宽度 */}
        <div
          className={`h-full overflow-hidden rounded-xl ${
            aiPanel.isDragging
              ? ""
              : "transition-[width] duration-300 ease-in-out"
          }`}
          style={{
            width: aiPanelCollapsible.isCollapsed
              ? "32px"
              : `${aiPanel.width}px`,
            flexShrink: 0,
            willChange: "width", // 提示浏览器优化宽度变化的渲染
            backfaceVisibility: "hidden", // 减少闪烁
            transform: "translateZ(0)", // 启用GPU加速
          }}
        >
          <AIPanel
            isCollapsed={aiPanelCollapsible.isCollapsed}
            isContentVisible={aiPanelCollapsible.isContentVisible}
            toggleCollapse={aiPanelCollapsible.toggleCollapse}
            defaultTab="chat"
          >
            {/* 注册面板 */}
            <Chat />
            <Docs />
            <Settings />
            <Tools />
          </AIPanel>
        </div>
      </div>

      {/* 使用新的 ContextMenu 组件 - AI Panel */}
      <ContextMenu
        isOpen={aiPanel.showContextMenu}
        setIsOpen={(isOpen) => aiPanel.setShowContextMenu(isOpen)}
        referenceElement={aiPanel.dividerRef.current}
        clickPosition={aiPanel.clickPosition}
        width="w-[120px]"
        placement="right-start"
      >
        <MenuItem onClick={aiPanel.handleResetWidth}>Reset Width</MenuItem>
      </ContextMenu>

      {/* 使用新的 ContextMenu 组件 - Sidebar */}
      <ContextMenu
        isOpen={sidebar.showContextMenu}
        setIsOpen={(isOpen) => sidebar.setShowContextMenu(isOpen)}
        referenceElement={sidebar.dividerRef.current}
        clickPosition={sidebar.clickPosition}
        width="w-[120px]"
        placement="right-start"
      >
        <MenuItem onClick={sidebar.handleResetWidth}>Reset Width</MenuItem>
      </ContextMenu>
    </main>
  );
}
