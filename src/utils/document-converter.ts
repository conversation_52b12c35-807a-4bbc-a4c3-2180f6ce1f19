/**
 * TipTap 与 Markdown 双向转换工具
 * 支持高亮、下划线等富文本格式的智能转换
 */

import { Editor } from "@tiptap/react";
import { generateHTML, generateJSON } from "@tiptap/html";
import { StarterKit } from "@tiptap/starter-kit";
import { Highlight } from "@tiptap/extension-highlight";
import { TextAlign } from "@tiptap/extension-text-align";
import { TextStyle } from "@tiptap/extension-text-style";
import { FontFamily } from "@tiptap/extension-font-family";
import { Underline } from "@tiptap/extension-underline";

// 扩展配置（与编辑器保持一致）
const extensions = [
  StarterKit,
  TextAlign.configure({
    types: ["heading", "paragraph", "title"],
  }),
  Highlight.configure({ multicolor: true }),
  TextStyle,
  FontFamily,
  Underline,
];

/**
 * 文档存储格式接口
 */
export interface DocumentData {
  /** TipTap JSON 格式（主存储） */
  contentJson: any;
  /** Markdown 格式（AI 友好） */
  contentMarkdown: string;
  /** HTML 格式（可选，用于预览） */
  contentHtml?: string;
}

/**
 * 自定义 Markdown 语法映射
 */
const CUSTOM_MARKDOWN_SYNTAX = {
  // 高亮语法：==文本== 或 <mark color="yellow">文本</mark>
  highlight: {
    pattern: /==([^=]+)==/g,
    replacement: '<mark>$1</mark>',
  },
  // 彩色高亮：<mark color="red">文本</mark>
  colorHighlight: {
    pattern: /<mark\s+color="([^"]+)">([^<]+)<\/mark>/g,
    replacement: '==$2==',
  },
  // 下划线：<u>文本</u>
  underline: {
    pattern: /<u>([^<]+)<\/u>/g,
    replacement: '_$1_',
  },
};

/**
 * TipTap JSON 转换为 Markdown
 */
export function tiptapToMarkdown(tiptapJson: any): string {
  try {
    // 1. 先转换为 HTML
    const html = generateHTML(tiptapJson, extensions);
    
    // 2. HTML 转 Markdown（简化版实现）
    let markdown = html
      // 移除不必要的标签
      .replace(/<\/?div[^>]*>/g, '')
      .replace(/<\/?span[^>]*>/g, '')
      .replace(/<br\s*\/?>/g, '\n')
      
      // 标题转换
      .replace(/<h1[^>]*>(.*?)<\/h1>/g, '# $1')
      .replace(/<h2[^>]*>(.*?)<\/h2>/g, '## $1')
      .replace(/<h3[^>]*>(.*?)<\/h3>/g, '### $1')
      .replace(/<h4[^>]*>(.*?)<\/h4>/g, '#### $1')
      .replace(/<h5[^>]*>(.*?)<\/h5>/g, '##### $1')
      .replace(/<h6[^>]*>(.*?)<\/h6>/g, '###### $1')
      
      // 段落转换
      .replace(/<p[^>]*>(.*?)<\/p>/g, '$1\n\n')
      
      // 格式化标记
      .replace(/<strong[^>]*>(.*?)<\/strong>/g, '**$1**')
      .replace(/<b[^>]*>(.*?)<\/b>/g, '**$1**')
      .replace(/<em[^>]*>(.*?)<\/em>/g, '*$1*')
      .replace(/<i[^>]*>(.*?)<\/i>/g, '*$1*')
      
      // 高亮转换（支持颜色）
      .replace(/<mark[^>]*style="[^"]*background-color:\s*([^;"]+)[^"]*"[^>]*>(.*?)<\/mark>/g, 
        (match, color, text) => {
          // 根据颜色映射到不同的标记
          const colorMap: Record<string, string> = {
            '#fef9c3': '==', // yellow
            '#dcfce7': '==', // green  
            '#e0f2fe': '==', // blue
            '#f3e8ff': '==', // purple
            '#ffe4e6': '==', // red
          };
          const marker = colorMap[color] || '==';
          return `${marker}${text}${marker}`;
        })
      .replace(/<mark[^>]*>(.*?)<\/mark>/g, '==$1==')
      
      // 下划线转换
      .replace(/<u[^>]*>(.*?)<\/u>/g, '<u>$1</u>')
      
      // 列表转换
      .replace(/<ul[^>]*>(.*?)<\/ul>/gs, (match, content) => {
        return content.replace(/<li[^>]*>(.*?)<\/li>/g, '- $1') + '\n';
      })
      .replace(/<ol[^>]*>(.*?)<\/ol>/gs, (match, content) => {
        let counter = 1;
        return content.replace(/<li[^>]*>(.*?)<\/li>/g, () => `${counter++}. $1`) + '\n';
      })
      
      // 清理多余的换行
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    return markdown;
  } catch (error) {
    console.error('TipTap to Markdown conversion failed:', error);
    return '';
  }
}

/**
 * Markdown 转换为 TipTap JSON
 */
export function markdownToTiptap(markdown: string): any {
  try {
    // 1. 预处理 Markdown，转换自定义语法
    let processedMarkdown = markdown
      // 高亮语法转换
      .replace(/==([^=]+)==/g, '<mark>$1</mark>')
      // 下划线语法转换（避免与斜体冲突）
      .replace(/(?<!_)_([^_\s][^_]*[^_\s])_(?!_)/g, '<u>$1</u>');

    // 2. 基础 Markdown 转 HTML（简化实现）
    let html = processedMarkdown
      // 标题
      .replace(/^#{6}\s+(.+)$/gm, '<h6>$1</h6>')
      .replace(/^#{5}\s+(.+)$/gm, '<h5>$1</h5>')
      .replace(/^#{4}\s+(.+)$/gm, '<h4>$1</h4>')
      .replace(/^#{3}\s+(.+)$/gm, '<h3>$1</h3>')
      .replace(/^#{2}\s+(.+)$/gm, '<h2>$1</h2>')
      .replace(/^#{1}\s+(.+)$/gm, '<h1>$1</h1>')
      
      // 粗体和斜体
      .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
      .replace(/\*([^*]+)\*/g, '<em>$1</em>')
      
      // 段落（简单处理）
      .split('\n\n')
      .map(para => para.trim() ? `<p>${para.replace(/\n/g, '<br>')}</p>` : '')
      .join('');

    // 3. HTML 转 TipTap JSON
    const tiptapJson = generateJSON(html, extensions);
    
    return tiptapJson;
  } catch (error) {
    console.error('Markdown to TipTap conversion failed:', error);
    return { type: 'doc', content: [] };
  }
}

/**
 * 从编辑器实例获取文档数据
 */
export function getDocumentData(editor: Editor): DocumentData {
  const contentJson = editor.getJSON();
  const contentMarkdown = tiptapToMarkdown(contentJson);
  const contentHtml = editor.getHTML();

  return {
    contentJson,
    contentMarkdown,
    contentHtml,
  };
}

/**
 * 将文档数据加载到编辑器
 */
export function loadDocumentData(editor: Editor, data: DocumentData): void {
  try {
    // 优先使用 JSON 格式（保持完整格式）
    if (data.contentJson) {
      editor.commands.setContent(data.contentJson);
    } else if (data.contentMarkdown) {
      // 备选：从 Markdown 转换
      const tiptapJson = markdownToTiptap(data.contentMarkdown);
      editor.commands.setContent(tiptapJson);
    }
  } catch (error) {
    console.error('Failed to load document data:', error);
  }
}

/**
 * 清理 Markdown 文本（用于 AI 处理）
 */
export function cleanMarkdownForAI(markdown: string): string {
  return markdown
    // 移除过多的换行
    .replace(/\n{3,}/g, '\n\n')
    // 移除 HTML 标签（保留语义）
    .replace(/<u>(.*?)<\/u>/g, '$1')
    .replace(/<mark[^>]*>(.*?)<\/mark>/g, '$1')
    // 标准化空格
    .replace(/\s+/g, ' ')
    .trim();
}
